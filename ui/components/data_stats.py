"""
数据统计组件
"""
import os
import csv
from typing import Dict, List, Any
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                               QGroupBox, QGridLayout, QPushButton, QScrollArea)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont


class DataStatsWidget(QWidget):
    """数据统计组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.setup_timer()
        self.update_stats()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(0, 0, 0, 0)

        # 文件信息组 - 移除固定高度，让它能够自适应拉伸
        files_group = QGroupBox("文件分布")
        # files_group.setMinimumHeight(400)  # 注释掉固定高度
        files_layout = QVBoxLayout(files_group)
        files_layout.setSpacing(8)
        files_layout.setContentsMargins(12, 8, 12, 8)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                border: none;
                background: #f5f5f5;
                width: 8px;
                margin: 0px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: #c0c0c0;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #a0a0a0;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
        """)

        # 创建内容容器
        self.files_content_widget = QWidget()
        self.files_content_widget.setStyleSheet("background-color: transparent;")

        # 内容布局
        self.files_content_layout = QVBoxLayout(self.files_content_widget)
        self.files_content_layout.setContentsMargins(8, 8, 8, 8)
        self.files_content_layout.setSpacing(6)

        self.files_info_label = QLabel("正在加载...")
        self.files_info_label.setStyleSheet("""
            font-size: 12px; 
            color: #333333;
            background-color: transparent;
            line-height: 1.5;
            padding: 4px;
        """)
        self.files_info_label.setWordWrap(True)
        self.files_info_label.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.files_content_layout.addWidget(self.files_info_label)
        self.files_content_layout.addStretch()

        # 将内容容器设置为滚动区域的widget
        scroll_area.setWidget(self.files_content_widget)

        # 让滚动区域占据大部分空间
        files_layout.addWidget(scroll_area, 1)  # 添加拉伸因子

        # 操作按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)
        button_layout.setContentsMargins(12, 0, 12, 8)

        self.refresh_btn = QPushButton("刷新统计")
        self.refresh_btn.clicked.connect(self.update_stats)
        button_layout.addWidget(self.refresh_btn)

        button_layout.addStretch()

        files_layout.addLayout(button_layout, 0)  # 按钮区域不拉伸

        # 让文件组占据所有可用空间
        layout.addWidget(files_group, 1)  # 添加拉伸因子

    def setup_timer(self):
        """设置定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_stats)
        self.timer.start(30000)  # 每30秒更新一次

    def update_stats(self):
        """更新统计信息"""
        try:
            # 统计data目录下的文件
            data_files_info = self.get_data_files_info()
            self.files_info_label.setText(data_files_info)

        except Exception as e:
            self.files_info_label.setText(f"统计失败: {str(e)}")

    def get_data_files_info(self) -> str:
        """获取data目录文件信息"""
        data_dir = "data"

        if not os.path.exists(data_dir):
            return "📁 data目录不存在"

        try:
            csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]

            if not csv_files:
                return "📁 data目录下没有CSV文件"

            info_lines = [f"📊 共有 {len(csv_files)} 个CSV文件\n"]

            total_size = 0
            total_records = 0

            for file in sorted(csv_files):
                file_path = os.path.join(data_dir, file)
                file_size = os.path.getsize(file_path)
                total_size += file_size

                # 统计记录数
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        reader = csv.reader(f)
                        # next(reader, [])  # 跳过表头
                        record_count = sum(1 for _ in reader)
                        total_records += record_count

                        info_lines.append(
                            f"📄 {file}\n   └ {record_count:,} 条记录 • {self.format_file_size(file_size)}\n"
                        )
                except Exception:
                    info_lines.append(f"❌ {file}: 读取失败\n")

            info_lines.append("─" * 30)
            info_lines.append(f"📈 总计: {total_records:,} 条记录")
            info_lines.append(f"💾 总大小: {self.format_file_size(total_size)}")

            return "\n".join(info_lines)

        except Exception as e:
            return f"❌ 读取data目录失败: {str(e)}"

    def format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"

        units = ['B', 'KB', 'MB', 'GB']
        unit_index = 0
        size = float(size_bytes)

        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1

        if unit_index == 0:
            return f"{int(size)} {units[unit_index]}"
        else:
            return f"{size:.1f} {units[unit_index]}"
