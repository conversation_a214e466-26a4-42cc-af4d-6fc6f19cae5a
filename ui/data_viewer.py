"""
数据查看器界面
"""
import csv
import os
import webbrowser
from typing import List, Dict, Any

from PySide6.QtCore import Qt, Signal, QThread, QObject
from PySide6.QtGui import QAction
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                               QTableWidget, QTableWidgetItem, QLabel,
                               QLineEdit, QComboBox, QGroupBox,
                               QHeaderView, QAbstractItemView, QMenu, QApplication,
                               QSplitter, QFileDialog, QMessageBox)

from core.data_manager import data_manager
from ui.components.data_stats import DataStatsWidget
from ui.message_box import CustomMessageBox


class DataLoader(QObject):
    """数据加载器"""
    data_loaded = Signal(list)
    error_occurred = Signal(str)

    def __init__(self):
        super().__init__()
        self.file_path = ""
        self._stop_requested = False

    def request_stop(self):
        self._stop_requested = True

    def load_data(self):
        try:
            if not self._stop_requested:
                data = data_manager.read_csv_data(self.file_path)
                if not self._stop_requested:
                    self.data_loaded.emit(data)
        except Exception as e:
            if not self._stop_requested:
                self.error_occurred.emit(str(e))

class DataViewer(QWidget):
    """数据查看器界面"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.data_loader = None
        self.current_file = ''
        self.original_data = []
        self.filtered_data = []
        self.load_thread = None
        self.available_files = []
        self.setup_ui()
        self.refresh_file_list()
        self.load_data()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 15, 20, 15)

        # 设置扁平化样式
        self.setStyleSheet("""
            QWidget {
                background-color: #ffffff;
                font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
            }
            QGroupBox {
                font-weight: 500;
                border: 1px solid #e0e0e0;
                margin-top: 8px;
                padding-top: 8px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 6px 0 6px;
                color: #333333;
                font-size: 13px;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 6px 12px;
                font-weight: 400;
                min-width: 60px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
            QPushButton:disabled {
                background-color: #BDBDBD;
            }
            QLineEdit, QComboBox {
                padding: 6px 8px;
                border: 1px solid #e0e0e0;
                background-color: white;
                font-size: 12px;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #2196F3;
                outline: none;
            }
            QTableWidget {
                gridline-color: #f0f0f0;
                background-color: white;
                alternate-background-color: #fafafa;
                border: 1px solid #e0e0e0;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #2196F3;
                color: white;
            }
            QHeaderView::section {
                background-color: #f5f5f5;
                padding: 8px;
                border: none;
                border-right: 1px solid #e0e0e0;
                font-weight: 500;
                color: #333333;
            }
            QLabel {
                color: #333333;
                font-size: 12px;
            }
        """)
        # 创建主分割器（左右布局）
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #e0e0e0;
                width: 2px;
            }
        """)
        # 左侧：统计信息区域
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 8, 0)
        left_layout.setSpacing(12)
        self.stats_widget = DataStatsWidget()
        # 移除固定宽度限制，让它可以拉伸
        # self.stats_widget.setMaximumWidth(250)  # 注释掉这行
        # 让统计组件占据所有可用空间，与右侧表格区域对应
        left_layout.addWidget(self.stats_widget)
        # 移除addStretch()，让统计组件填满整个左侧区域
        # left_layout.addStretch()  # 注释掉这行

        main_splitter.addWidget(left_widget)

        # 右侧：数据表格区域
        right_widget = QWidget()
        table_layout = QVBoxLayout(right_widget)
        table_layout.setContentsMargins(8, 0, 0, 0)
        table_layout.setSpacing(12)

        # 优化的操作工具栏
        toolbar_group = QGroupBox("操作工具")
        toolbar_group.setMaximumHeight(120)
        toolbar_layout = QVBoxLayout(toolbar_group)
        toolbar_layout.setContentsMargins(12, 8, 12, 8)
        toolbar_layout.setSpacing(8)

        # 第一行：文件选择和主要操作
        top_row = QHBoxLayout()
        top_row.setSpacing(12)

        # 文件选择区域
        file_container = QWidget()
        file_layout = QHBoxLayout(file_container)
        file_layout.setContentsMargins(0, 0, 0, 0)
        file_layout.setSpacing(8)

        file_label = QLabel("数据文件:")
        file_label.setStyleSheet("""
            font-weight: 500;
            color: #333333;
            font-size: 12px;
            min-width: 60px;
        """)
        file_layout.addWidget(file_label)

        self.file_selector = QComboBox()
        self.file_selector.currentTextChanged.connect(self.on_file_changed)
        self.file_selector.setMinimumWidth(180)
        self.file_selector.setStyleSheet("""
            QComboBox {
                font-size: 12px;
                padding: 6px 10px;
                min-height: 24px;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                background-color: white;
            }
            QComboBox:hover {
                border-color: #2196F3;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
        """)
        file_layout.addWidget(self.file_selector)

        top_row.addWidget(file_container)

        # 操作按钮区域
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(8)

        # 统一的按钮样式
        button_style = """
            QPushButton {
                border: none;
                padding: 6px 12px;
                font-weight: 500;
                font-size: 12px;
                min-width: 70px;
                min-height: 28px;
                border-radius: 4px;
            }
        """

        self.refresh_files_btn = QPushButton("刷新文件")
        self.refresh_files_btn.clicked.connect(self.refresh_file_list)
        self.refresh_files_btn.setStyleSheet(button_style + """
            QPushButton {
                background-color: #4CAF50;
                color: white;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        button_layout.addWidget(self.refresh_files_btn)

        self.export_btn = QPushButton("导出数据")
        self.export_btn.clicked.connect(self.export_data)
        self.export_btn.setStyleSheet(button_style + """
            QPushButton {
                background-color: #00BCD4;
                color: white;
            }
            QPushButton:hover {
                background-color: #00ACC1;
            }
        """)
        button_layout.addWidget(self.export_btn)

        top_row.addWidget(button_container)

        top_row.addStretch()
        toolbar_layout.addLayout(top_row)

        # 第二行：搜索功能
        search_row = QHBoxLayout()
        search_row.setSpacing(12)

        # 搜索区域
        search_container = QWidget()
        search_layout = QHBoxLayout(search_container)
        search_layout.setContentsMargins(0, 0, 0, 0)
        search_layout.setSpacing(8)

        search_label = QLabel("数据搜索:")
        search_label.setStyleSheet("""
            font-weight: 500;
            color: #333333;
            font-size: 12px;
            min-width: 60px;
        """)
        search_layout.addWidget(search_label)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入关键词搜索...")
        self.search_input.textChanged.connect(self.filter_data)
        self.search_input.setMinimumWidth(180)
        self.search_input.setStyleSheet("""
            QLineEdit {
                font-size: 12px;
                padding: 6px 10px;
                min-height: 24px;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #2196F3;
            }
            QLineEdit::placeholder {
                color: #999999;
                font-style: normal;
            }
        """)
        search_layout.addWidget(self.search_input)

        search_row.addWidget(search_container)

        search_row.addStretch()
        toolbar_layout.addLayout(search_row)

        table_layout.addWidget(toolbar_group)

        # 数据表格
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)

        # 启用滚动条
        self.table.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.table.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 隐藏表头
        self.table.horizontalHeader().setVisible(False)
        self.table.verticalHeader().setVisible(False)

        # 设置列宽调整模式
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Interactive)

        # 设置表格样式
        self.table.setStyleSheet("""
            QTableWidget {
                min-height: 500px;
                gridline-color: #f0f0f0;
                background-color: white;
                alternate-background-color: #fafafa;
                border: 1px solid #e0e0e0;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #2196F3;
                color: white;
            }
            QScrollBar:horizontal {
                border: none;
                background: #f5f5f5;
                height: 12px;
                margin: 0px;
            }
            QScrollBar::handle:horizontal {
                background: #c0c0c0;
                border-radius: 6px;
                min-width: 20px;
            }
            QScrollBar::handle:horizontal:hover {
                background: #a0a0a0;
            }
        """)

        table_layout.addWidget(self.table)

        # 状态信息
        self.status_label = QLabel("准备就绪")
        self.status_label.setStyleSheet("font-size: 10px; color: #666666; padding: 2px;")
        table_layout.addWidget(self.status_label)

        main_splitter.addWidget(right_widget)

        # 设置分割器比例 (左:右 = 1:3) - 统计信息:数据表格
        main_splitter.setSizes([200, 600])

        layout.addWidget(main_splitter)

    # 在DataViewer中使用
    def load_data(self):
        """加载数据"""
        if not os.path.exists(self.current_file):
            self.status_label.setText(f"文件不存在: {self.current_file}")
            self.table.setRowCount(0)
            self.table.setColumnCount(0)
            return

        self.status_label.setText("正在加载数据...")

        # 使用线程加载数据
        if self.load_thread and self.load_thread.isRunning():
            self.data_loader.request_stop()
            self.load_thread.quit()
            if not self.load_thread.wait(3000):
                self.load_thread.terminate()
                self.load_thread.wait()

        self.load_thread = QThread()
        self.data_loader = DataLoader()
        self.data_loader.moveToThread(self.load_thread)
        self.data_loader.file_path = self.current_file
        self.data_loader.data_loaded.connect(self.on_data_loaded)
        self.data_loader.error_occurred.connect(self.on_load_error)
        self.load_thread.started.connect(self.data_loader.load_data)
        self.load_thread.start()

    def on_data_loaded(self, data: List[List[str]]):
        """数据加载完成"""
        self.original_data = data
        self.filtered_data = data.copy()

        self.update_table()
        self.status_label.setText(f"已加载 {len(data)} 条记录")

        # 更新统计信息
        self.stats_widget.update_stats()

    def on_load_error(self, error_msg: str):
        """数据加载错误"""
        self.status_label.setText(f"加载失败: {error_msg}")
        QMessageBox.critical(self, "加载失败", f"加载数据时出错:\n{error_msg}")

    def update_table(self):
        """更新表格显示"""
        # 直接设置数据，不处理表头
        self.table.setRowCount(len(self.filtered_data))
        if self.filtered_data:
            self.table.setColumnCount(len(self.filtered_data[0]))
        else:
            self.table.setColumnCount(0)

        # 填充数据
        for row_idx, row_data in enumerate(self.filtered_data):
            for col_idx, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # 设置为只读
                # 设置文本对齐方式
                item.setTextAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
                self.table.setItem(row_idx, col_idx, item)

        # 调整列宽
        self.table.resizeColumnsToContents()

        # 限制最大列宽
        for i in range(self.table.columnCount()):
            if self.table.columnWidth(i) > 300:
                self.table.setColumnWidth(i, 300)

    def filter_data(self):
        """筛选数据"""
        search_text = self.search_input.text().lower()
        if not search_text:
            self.filtered_data = self.original_data.copy()
        else:
            self.filtered_data = []
            for row in self.original_data:
                if any(search_text in str(cell).lower() for cell in row):
                    self.filtered_data.append(row)

        self.update_table()
        self.status_label.setText(f"显示 {len(self.filtered_data)} / {len(self.original_data)} 条记录")

    def export_data(self):
        """导出数据"""
        if not self.filtered_data:
            CustomMessageBox.warning(self, "警告", "没有数据可导出")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出数据", f"exported_data.csv", "CSV文件 (*.csv)"
            , options=QFileDialog.Option.DontUseNativeDialog
        )

        if file_path:
            try:
                with open(file_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerows(self.filtered_data)  # 写入数据
                CustomMessageBox.information(self, "导出成功", f"数据已导出到: {file_path}")
            except Exception as e:
                CustomMessageBox.critical(self, "导出失败", f"导出数据时出错: {str(e)}")

    def refresh_file_list(self):
        """刷新文件列表"""
        # 使用数据管理器获取文件列表
        self.available_files = data_manager.get_all_data_files()

        # 更新下拉框
        self.file_selector.clear()
        for file_path, display_name in self.available_files:
            self.file_selector.addItem(display_name, file_path)

        # 设置当前选中的文件
        current_index = -1
        for i, (file_path, _) in enumerate(self.available_files):
            if file_path == self.current_file:
                current_index = i
                break

        if current_index >= 0:
            self.file_selector.setCurrentIndex(current_index)
        elif self.available_files:
            self.current_file = self.available_files[0][0]
            self.file_selector.setCurrentIndex(0)
        self.load_data()

    def on_file_changed(self):
        """文件选择改变"""
        current_data = self.file_selector.currentData()
        if current_data and current_data != self.current_file:
            self.current_file = current_data
            self.load_data()
