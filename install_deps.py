#!/usr/bin/env python3
"""
依赖安装脚本
"""
import subprocess
import sys
import os

def install_requirements(requirements_file="requirements.txt"):
    """安装依赖包"""
    if not os.path.exists(requirements_file):
        print(f"❌ 依赖文件不存在: {requirements_file}")
        return False
    
    print(f"📦 开始安装依赖: {requirements_file}")
    
    try:
        # 升级pip
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # 安装依赖
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "-r", requirements_file,
            "--upgrade"
        ])
        
        print("✅ 依赖安装完成!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="安装项目依赖")
    parser.add_argument(
        "--dev", 
        action="store_true", 
        help="安装开发环境依赖"
    )
    parser.add_argument(
        "--minimal", 
        action="store_true", 
        help="安装最小化依赖"
    )
    
    args = parser.parse_args()
    
    if args.dev:
        requirements_file = "requirements-dev.txt"
    elif args.minimal:
        requirements_file = "requirements-minimal.txt"
    else:
        requirements_file = "requirements.txt"
    
    success = install_requirements(requirements_file)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()