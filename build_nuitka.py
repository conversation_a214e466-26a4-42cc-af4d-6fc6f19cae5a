#!/usr/bin/env python3
"""
使用Nuitka打包微信公众号自动化管理工具
解决comtypes模块缺失问题
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def build_with_nuitka():
    """使用Nuitka构建应用程序"""
    
    print("开始使用Nuitka打包...")
    
    # 基本的nuitka命令
    cmd = [
        sys.executable, "-m", "nuitka",
        "--standalone",
        "--jobs=4",
        "--onefile",
        "--windows-disable-console",
        "--enable-plugin=pyside6",
        "--windows-icon-from-ico=icon.ico" if os.path.exists("icon.ico") else "",
        
        # 包含必要的模块
        "--include-module=comtypes",
        "--include-module=comtypes.stream",
        "--include-module=comtypes.client",
        "--include-module=comtypes.automation",
        "--include-module=pythoncom",
        "--include-module=pywintypes",
        "--include-module=win32com",
        "--include-module=win32com.client",
        "--include-module=pywinauto",
        "--include-module=pywinauto.controls",
        "--include-module=pywinauto.application",
        
        # 包含项目模块
        "--include-package=ui",
        "--include-package=core",
        "--include-package=config",
        "--include-package=weixin",
        
        # 包含数据文件
        "--include-data-dir=resource=resource",
        "--include-data-dir=data=data",
        "--include-data-dir=weixin=weixin",

        # 输出设置
        "--output-filename=微信公众号自动化管理工具.exe",
        "--output-dir=dist_nuitka",
        
        # 优化设置
        "--assume-yes-for-downloads",
        "--show-progress",
        "--show-memory",
        
        # 主程序文件
        "main.py"
    ]
    
    # 过滤空字符串
    cmd = [arg for arg in cmd if arg]
    
    print("执行命令:")
    print(" ".join(cmd))
    print()
    
    try:
        # 执行nuitka命令
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("\n✅ Nuitka打包成功!")
        
        # 复制必要的文件到输出目录
        copy_additional_files()
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Nuitka打包失败: {e}")
        return False
    except Exception as e:
        print(f"\n❌ 打包过程出错: {e}")
        return False

def copy_additional_files():
    """复制额外的文件到输出目录"""
    
    dist_dir = Path("dist_nuitka")
    if not dist_dir.exists():
        print("输出目录不存在，跳过文件复制")
        return
    
    # 要复制的文件和目录
    items_to_copy = [
        ("README.md", "README.md"),
        ("CHANGELOG.md", "CHANGELOG.md"),
        ("LICENSE", "LICENSE"),
        ("requirements.txt", "requirements.txt"),
        ("resource", "resource"),
        ("data", "data"),
        ("downloads", "downloads"),
    ]
    
    for src, dst in items_to_copy:
        src_path = Path(src)
        dst_path = dist_dir / dst
        
        try:
            if src_path.exists():
                if src_path.is_file():
                    shutil.copy2(src_path, dst_path)
                    print(f"已复制文件: {src} -> {dst_path}")
                elif src_path.is_dir():
                    if dst_path.exists():
                        shutil.rmtree(dst_path)
                    shutil.copytree(src_path, dst_path)
                    print(f"已复制目录: {src} -> {dst_path}")
        except Exception as e:
            print(f"复制 {src} 时出错: {e}")
    
    # 创建使用说明
    create_usage_guide(dist_dir)

def create_usage_guide(dist_dir):
    """创建使用说明文件"""
    
    usage_content = """微信公众号自动化管理工具 - 使用说明

🚀 快速开始:
1. 双击"微信公众号自动化管理工具.exe"启动程序
2. 首次使用请在"设置"中配置微信程序路径

📁 目录说明:
- resource/ : 配置文件目录
  - config.json : 程序配置
  - accounts.csv : 账号数据
  - official_infos.json : 公众号信息
- data/ : 数据存储目录
  - *.csv : 各账号采集数据

📋 主要功能:
- 账号管理: 添加、编辑、删除公众号账号
- 批量关注: 自动关注指定的公众号列表  
- 数据采集: 自动采集公众号信息和文章数据
- 数据查看: 查看、搜索、筛选、导出数据
- 全文下载: 下载文章全文为MD/HTML格式

💻 系统要求:
- Windows 10/11
- 微信PC版 (最新版本)

⚠️ 重要提醒:
- 使用时请遵守微信使用条款，避免频繁操作
- 首次启动可能需要几秒钟，请耐心等待

🔧 技术信息:
- 打包工具: Nuitka
- Python版本: 3.12
- 构建时间: """ + str(Path().resolve()) + """

如有问题，请查看README.md文档或联系技术支持。
"""
    
    usage_file = dist_dir / "使用说明.txt"
    try:
        with open(usage_file, 'w', encoding='utf-8') as f:
            f.write(usage_content)
        print(f"已创建使用说明: {usage_file}")
    except Exception as e:
        print(f"创建使用说明失败: {e}")

def check_dependencies():
    """检查依赖是否安装"""
    
    print("检查依赖...")
    
    try:
        import nuitka
        # Nuitka的版本信息在不同版本中位置不同
        try:
            version = nuitka.__version__
        except AttributeError:
            try:
                from nuitka.Version import getNuitkaVersion
                version = getNuitkaVersion()
            except ImportError:
                version = "未知版本"
        print(f"✅ Nuitka版本: {version}")
    except ImportError:
        print("❌ 未安装Nuitka，请运行: pip install nuitka")
        return False
    
    try:
        import PySide6
        print(f"✅ PySide6版本: {PySide6.__version__}")
    except ImportError:
        print("❌ 未安装PySide6")
        return False
    
    # 检查可选依赖
    optional_deps = [
        ("comtypes", "comtypes"),
        ("pythoncom", "pywin32"),
        ("pywinauto", "pywinauto"),
    ]
    
    for module, package in optional_deps:
        try:
            __import__(module)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"⚠️  {package} 未安装，可能影响部分功能")
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("微信公众号自动化管理工具 - Nuitka打包脚本")
    print("=" * 60)
    print()
    
    # 检查依赖
    if not check_dependencies():
        print("\n请先安装必要的依赖")
        sys.exit(1)
    
    print()
    
    # 开始打包
    success = build_with_nuitka()
    
    if success:
        print("\n🎉 打包完成!")
        print("输出目录: dist_nuitka/")
        print("可执行文件: dist_nuitka/微信公众号自动化管理工具.exe")
    else:
        print("\n💥 打包失败!")
        sys.exit(1)
