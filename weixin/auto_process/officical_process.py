import csv
import hashlib
import logging
import os
import random
import re
import time
import traceback
import warnings
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from urllib.parse import urlparse

import pyautogui
import pyperclip
from pywinauto import mouse, Desktop, ElementNotFoundError

from core.common_util import send_message, log_info, log_error, log_warning, update_crawler_time
from core.official_down import WeChatArticleCrawler
from weixin.weixin_ui_api import Independent_window, WechatTools, Systemsettings

# 常量定义
WECHAT_ARTICLE_URL_PREFIX = 'https://mp.weixin.qq.com/s/'
MAX_LINK_RETRY_COUNT = 10
MAX_SCROLL_ATTEMPTS = 10
MAX_PAGE_CHANGE_RETRY = 15
PADDING = 3
DEFAULT_DELAY_RANGE = (1, 3)
WHEEL_DIST_RANGE = (15, 17)

# 正则表达式模式，匹配包含阅读和点赞信息的标题
READING_LIKE_PATTERN = re.compile(r'(?:阅读\s*(\d*[,.万]*)|\s*赞\s*(\d*[,.万]*))')

# 初始化下载器
crawler = WeChatArticleCrawler()

# 初始化COM组件，避免pywinauto警告
try:
    import pythoncom

    pythoncom.CoInitialize()
except ImportError:
    pass

# 过滤pywinauto的COM线程模式警告
warnings.filterwarnings("ignore", message="Revert to STA COM threading mode", category=UserWarning)


def generate_url_hash(url: str) -> str:
    """从微信文章URL中提取唯一编码

    Args:
        url: 微信文章URL

    Returns:
        str: 唯一标识符
    """
    try:
        parsed = urlparse(url)
        if '/s/' in url:
            path_parts = parsed.path.split('/s/')
            if len(path_parts) > 1:
                unique_id = path_parts[1]
                return unique_id.split('?')[0]
        return hashlib.md5(url.encode('utf-8')).hexdigest()
    except Exception:
        return hashlib.md5(url.encode('utf-8')).hexdigest()


def check_control_visibility(window, target_control) -> bool:
    """检查控件是否在窗口可见区域内

    Args:
        window: 窗口对象
        target_control: 目标控件

    Returns:
        bool: 是否可见
    """
    try:
        target_rect = target_control.rectangle()
        window_rect = window.rectangle()
        return (target_rect.top >= window_rect.top and
                target_rect.bottom <= window_rect.bottom)
    except Exception:
        return False


def close_windows(*windows) -> None:
    """统一的窗口关闭方法

    Args:
        *windows: 要关闭的窗口对象
    """
    for window in windows:
        if window:
            try:
                window.close()
            except Exception:
                pass


def random_delay(min_sec: float = 1.0, max_sec: float = 3.0) -> None:
    """随机延迟

    Args:
        min_sec: 最小延迟秒数
        max_sec: 最大延迟秒数
    """
    time.sleep(round(random.uniform(min_sec, max_sec), 2))


class OfficialProcess:
    """公众号处理类"""

    def __init__(self):
        self.weixin_path: Optional[str] = None
        self.download_config = {
            'enable': False,
            'format': 'HTML',
            'delay': 2
        }
        self.stop_requested = False
        self.logger = logging.getLogger(__name__)

    def request_stop(self) -> None:
        """请求停止当前操作"""
        self.stop_requested = True
        log_info("收到停止请求")

    def reset_stop_flag(self) -> None:
        """重置停止标志"""
        self.stop_requested = False

    def is_stop_requested(self, enable_time_limit: bool = False,
                          start_time: str = "", end_time: str = "") -> bool:
        """检查是否请求停止

        Args:
            enable_time_limit: 是否启用时间限制
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            bool: 是否请求停止
        """
        if enable_time_limit and not WechatTools.is_within_time_range(start_time, end_time):
            log_info("当前时间段内不允许运行，挂起子进程等待...")

            # 挂起子进程直到允许运行时间
            while enable_time_limit and not WechatTools.is_within_time_range(start_time, end_time):
                # 检查是否收到停止请求
                if self.stop_requested:
                    log_info("收到停止请求，退出挂起状态")
                    return True

                # 等待一段时间后再次检查
                time.sleep(300)  # 每5分钟检查一次

            log_info("已进入允许运行时间段，继续执行任务")
            return False

        return self.stop_requested

    def set_download_config(self, config: Dict) -> None:
        """设置下载配置

        Args:
            config: 下载配置字典
        """
        self.download_config = config
        crawler.set_download_config(config)
        send_message('log', f'设置下载配置 - 全文下载: {config["enable"]} '
                            f'下载格式: {config["format"]} 延迟: {config["delay"]}')

    def download_article_sync(self, url: str, title: str,
                              official_account: str, url_hash: str) -> bool:
        """同步下载单篇文章全文

        Args:
            url: 文章URL
            title: 文章标题
            official_account: 公众号账号
            url_hash: URL哈希值

        Returns:
            bool: 下载是否成功
        """
        try:
            log_info(f"开始下载文章全文: {title}")
            content_result = crawler.process_article(
                url, official_account=official_account, url_hash=url_hash
            )

            if content_result and content_result.get('success'):
                log_info(f"✓ 文章下载成功: {title}")
                return True
            else:
                log_info(f"✗ 文章下载失败: {title} - 内容获取失败")
                return False

        except Exception as e:
            error_msg = f"✗ 文章下载异常: {title} - {traceback.format_exc()}"
            log_error(error_msg)
            self.logger.exception(error_msg)
            return False

    def add_to_download_queue(self, url: str, title: str,
                              official_account: str, url_hash: str) -> Optional[bool]:
        """添加文章到下载队列

        Args:
            url: 文章URL
            title: 文章标题
            official_account: 公众号账号
            url_hash: URL哈希值

        Returns:
            Optional[bool]: 下载结果，None表示未启用下载
        """
        if not self.download_config['enable']:
            return None

        log_info(f"全文已添加到下载队列: {title} - {url}")
        return self.download_article_sync(url, title, official_account, url_hash)

    def _filter_reading_like(self, text: str) -> bool:
        """过滤包含阅读和点赞信息的文本

        Args:
            text: 要检查的文本

        Returns:
            bool: 是否匹配
        """
        match = READING_LIKE_PATTERN.match(text)
        if match:
            reading, likes = match.groups()
            return ((reading and re.match(r'\d+', reading)) or
                    (likes and re.match(r'\d+', likes)))
        return False

    def _reload_message_list(self, official_window):
        """重新加载消息列表

        Args:
            official_window: 公众号窗口

        Returns:
            list: 可见的消息元素列表
        """
        official_window.set_focus()
        time.sleep(2)

        detail_items = official_window.child_window(
            control_type='Document',
            class_name='Chrome_RenderWidgetHostHWND'
        ).descendants(control_type="Text")

        return [element for element in detail_items
                if self._filter_reading_like(element.window_text())]

    def _get_duplicate_list(self, official_account: str) -> List[str]:
        """获取重复文章列表

        Args:
            official_account: 公众号账号

        Returns:
            List[str]: 已存在的文章URL列表
        """
        csv_file_path = f"data/{official_account}.csv"
        dup_list = []

        if os.path.isfile(csv_file_path):
            try:
                with open(csv_file_path, mode='r', encoding='utf-8') as file:
                    reader = csv.reader(file)
                    dup_list = [row[-1] for row in reader if row]
            except Exception as e:
                log_error(f"读取重复列表失败: {e}")

        return dup_list

    def _calculate_center_coords(self, rect) -> Tuple[int, int]:
        """计算矩形中心坐标

        Args:
            rect: 矩形对象

        Returns:
            Tuple[int, int]: 中心坐标(x, y)
        """
        center_x = (rect.left + rect.right) // 2 - PADDING
        center_y = (rect.top + rect.bottom) // 2 - PADDING
        return center_x, center_y

    def _scroll_to_next(self, detail_items_visibility: List, next_index: int,
                        official_window) -> Tuple[bool, List]:
        """滚动到下一个元素

        Args:
            detail_items_visibility: 可见元素列表
            next_index: 下一个索引
            official_window: 公众号窗口

        Returns:
            Tuple[bool, List]: (是否有变化, 新的元素列表)
        """
        old_num = len(detail_items_visibility)

        if next_index > 0:
            last_detail = detail_items_visibility[next_index - 1]
            last_detail_rect = last_detail.rectangle()
            center_x, center_y = self._calculate_center_coords(last_detail_rect)

            random_delay(*DEFAULT_DELAY_RANGE)
            mouse.scroll(coords=(center_x, center_y), wheel_dist=-1)

        new_detail_items = self._reload_message_list(official_window)
        cache_change = old_num != len(new_detail_items)

        log_info(f'滚动结果: 变化={cache_change}, 新数量={len(new_detail_items)}')
        return cache_change, new_detail_items

    def _get_article_link(self, detail, get_num: int, cache_num: int) -> Tuple[str, str]:
        """获取文章链接和标题

        Args:
            detail: 详情元素
            get_num: 当前获取数量
            cache_num: 缓存数量

        Returns:
            Tuple[str, str]: (文章链接, 文章标题)
        """
        desktop = Desktop(**Independent_window.Desktop)
        detail_rect = detail.rectangle()
        center_x, center_y = self._calculate_center_coords(detail_rect)

        article_link = ''
        detail_name = ''
        link_retry = 0

        while not article_link.startswith(WECHAT_ARTICLE_URL_PREFIX) and link_retry < MAX_LINK_RETRY_COUNT:
            if self.is_stop_requested():
                log_info("检测到停止请求，退出链接获取")
                break

            link_retry += 1
            detail_browser = None

            try:
                random_delay(*DEFAULT_DELAY_RANGE)
                mouse.move(coords=(center_x, center_y))
                log_info(f'[{get_num}/{cache_num}] 第{link_retry}次尝试获取链接')

                detail.click_input()
                random_delay(*DEFAULT_DELAY_RANGE)

                detail_browser = desktop.window(
                    title='微信',
                    class_name='Chrome_WidgetWin_0',
                    control_type='Pane'
                )

                if not detail_browser.exists():
                    log_info(f'[{get_num}/{cache_num}] 浏览器窗口不存在，重试')
                    continue

                # 获取文章标题
                detail_name = self._get_article_title(detail_browser)

                # 获取文章链接
                article_link = self._copy_article_link(detail_browser, get_num, cache_num)

            except Exception as e:
                log_error(f'[{get_num}/{cache_num}] 操作详情页异常: {traceback.format_exc()}')
                article_link = ''
                detail_name = ''
                random_delay(*DEFAULT_DELAY_RANGE)
            finally:
                if detail_browser:
                    detail_browser.close()
                    log_info(f'[{get_num}/{cache_num}] 关闭详情页')

        return article_link, detail_name

    def _get_article_title(self, detail_browser) -> str:
        """获取文章标题

        Args:
            detail_browser: 详情浏览器窗口

        Returns:
            str: 文章标题
        """
        detail_name = ''
        name_count = 5

        while (detail_name == '' or detail_name == '微信公众平台') and name_count > 0:
            time.sleep(0.5)
            try:
                tab_items = detail_browser.child_window(
                    title="微信", control_type="Pane"
                ).descendants(control_type="TabItem")

                if tab_items:
                    detail_name = tab_items[0].window_text()
            except Exception:
                pass
            name_count -= 1

        return detail_name

    def _copy_article_link(self, detail_browser, get_num: int, cache_num: int) -> str:
        """复制文章链接

        Args:
            detail_browser: 详情浏览器窗口
            get_num: 当前获取数量
            cache_num: 缓存数量

        Returns:
            str: 文章链接
        """
        try:
            more_item = detail_browser.child_window(title="更多")
            if not more_item.exists():
                log_warning(f'[{get_num}/{cache_num}] 更多按钮不存在')
                return ''

            more_item.click_input()
            random_delay(*DEFAULT_DELAY_RANGE)

            link_item = detail_browser.child_window(title="复制链接", control_type="MenuItem")
            if not link_item.exists():
                log_warning(f'[{get_num}/{cache_num}] 复制链接按钮不存在')
                return ''

            link_item.click_input()
            random_delay(*DEFAULT_DELAY_RANGE)

            return pyperclip.paste()

        except Exception as e:
            log_error(f'[{get_num}/{cache_num}] 复制链接失败: {traceback.format_exc()}')
            return ''

    def _save_article_data(self, csv_file_path: str, current_date: str,
                           url_hash: str, detail_name: str, article_link: str) -> None:
        """保存文章数据到CSV文件

        Args:
            csv_file_path: CSV文件路径
            current_date: 当前日期
            url_hash: URL哈希值
            detail_name: 文章标题
            article_link: 文章链接
        """
        try:
            with open(csv_file_path, mode='a', newline='', encoding='utf-8') as file:
                writer = csv.writer(file)
                writer.writerow([current_date, url_hash, detail_name, article_link])
        except Exception as e:
            log_error(f"保存文章数据失败: {e}")

    def crawl_official_detail(self, official_window, official_account: str,
                              official_name: str, limit_count: int, stop_exist_count: int) -> None:
        """采集公众号详情

        Args:
            official_window: 公众号窗口
            official_account: 公众号账号
            official_name: 公众号名称
            limit_count: 限制采集数量
            stop_exist_count: 停止存在计数
        """
        log_info(f'开始采集公众号: {official_name}({official_account})')

        detail_items_visibility = self._reload_message_list(official_window)
        cache_num = len(detail_items_visibility)

        log_info(f'获取消息列表中数据：{cache_num}')
        if not cache_num:
            log_info(f"公众号{official_account}，{official_name}，采集结束 [0/{cache_num}]")
            return

        # 滚动到最新消息
        self._scroll_to_latest_message(official_window, detail_items_visibility)

        # 获取重复列表
        dup_list = self._get_duplicate_list(official_account)
        csv_file_path = f"data/{official_account}.csv"

        # 采集变量初始化
        get_num = 0
        new_num = 0
        first_get = True
        exist_count = 0

        def check_need_crawl():
            return limit_count > get_num and stop_exist_count > exist_count

        # 主采集循环
        while check_need_crawl():
            detail_items_visibility = self._reload_message_list(official_window)

            if not first_get and len(detail_items_visibility) == cache_num:
                log_info('消息列表无新数据')
                break

            first_get = False
            cache_num = len(detail_items_visibility)

            # 处理当前页面的消息
            while check_need_crawl() and get_num < len(detail_items_visibility):
                if self.is_stop_requested():
                    log_info("检测到停止请求，退出采集")
                    return
                get_num += 1

                success = self._process_single_message(
                    detail_items_visibility, get_num, cache_num, official_window,
                    dup_list, csv_file_path, official_account, exist_count
                )

                if success:
                    new_num += 1
                    exist_count = 0
                elif success is False:  # 重复内容
                    exist_count += 1


            # 翻页处理
            if check_need_crawl():
                page_change = self._handle_page_change(detail_items_visibility, get_num, official_window)
                log_info(f"翻页结果: {page_change}")

        # 回写采集结束时间
        update_crawler_time(official_account, 'end', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        log_info(f"公众号采集结束 [{get_num}/{cache_num}] "
                 f"限制数量={limit_count} 新增={new_num} 公众号={official_name}")

    def _scroll_to_latest_message(self, official_window, detail_items_visibility: List) -> None:
        """滚动到最新消息

        Args:
            official_window: 公众号窗口
            detail_items_visibility: 可见元素列表
        """
        if not detail_items_visibility:
            return

        first_detail = detail_items_visibility[0]
        while not check_control_visibility(official_window, first_detail):
            visibility_detail = next(
                (item for item in detail_items_visibility
                 if check_control_visibility(official_window, item)), None
            )

            if visibility_detail:
                first_detail_rect = visibility_detail.rectangle()
                center_x, center_y = self._calculate_center_coords(first_detail_rect)
                random_delay(*DEFAULT_DELAY_RANGE)
                log_info('滚动到最新消息')
                mouse.scroll(coords=(center_x, center_y),
                             wheel_dist=random.randint(*WHEEL_DIST_RANGE))
            else:
                break

    def _process_single_message(self, detail_items_visibility: List, get_num: int,
                                cache_num: int, official_window, dup_list: List[str],
                                csv_file_path: str, official_account: str, exist_count: int) -> Optional[bool]:
        """处理单个消息

        Args:
            detail_items_visibility: 可见元素列表
            get_num: 当前获取数量
            cache_num: 缓存数量
            official_window: 公众号窗口
            dup_list: 重复列表
            csv_file_path: CSV文件路径
            official_account: 公众号账号
            exist_count: 存在计数

        Returns:
            Optional[bool]: True=成功, False=重复, None=跳过
        """
        try:
            detail = detail_items_visibility[get_num]
            if not detail or not hasattr(detail, 'element_info'):
                log_warning(f'[{get_num}/{cache_num}] 无效的详情元素，跳过')
                return None

            random_delay(*DEFAULT_DELAY_RANGE)

            # 确保元素可见
            if not self._ensure_element_visible(detail, official_window, get_num):
                log_warning(f'[{get_num}/{cache_num}] 无法使元素可见，跳过')
                return None

            # 获取文章链接和标题
            article_link, detail_name = self._get_article_link(detail, get_num, cache_num)

            if self.is_stop_requested():
                log_info("检测到停止请求，退出采集")
                return None

            if not article_link:
                log_info(f'跳过异常链接 [{get_num}/{cache_num}]')
                return None

            if article_link in dup_list:
                log_info(f'跳过重复内容 [{get_num}/{cache_num}] 存在计数={exist_count}')
                return False

            # 处理新文章
            return self._handle_new_article(
                article_link, detail_name, official_account, csv_file_path, get_num, cache_num
            )

        except Exception as e:
            log_error(f'[{get_num}/{cache_num}] 处理详情元素时出错: {traceback.format_exc()}')
            return None

    def _ensure_element_visible(self, detail, official_window, get_num: int) -> bool:
        """确保元素可见

        Args:
            detail: 详情元素
            official_window: 公众号窗口
            get_num: 当前获取数量

        Returns:
            bool: 是否成功使元素可见
        """
        scroll_attempts = 0
        while not check_control_visibility(official_window, detail) and scroll_attempts < MAX_SCROLL_ATTEMPTS:
            if self.is_stop_requested():
                log_info("检测到停止请求，退出滚动")
                return False

            _, _ = self._scroll_to_next([], get_num, official_window)
            scroll_attempts += 1

        return scroll_attempts < MAX_SCROLL_ATTEMPTS

    def _handle_new_article(self, article_link: str, detail_name: str,
                            official_account: str, csv_file_path: str,
                            get_num: int, cache_num: int) -> bool:
        """处理新文章

        Args:
            article_link: 文章链接
            detail_name: 文章标题
            official_account: 公众号账号
            csv_file_path: CSV文件路径
            get_num: 当前获取数量
            cache_num: 缓存数量

        Returns:
            bool: 处理是否成功
        """
        try:
            # 生成唯一标识
            url_hash = generate_url_hash(article_link)
            current_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 下载文章全文
            if self.download_config['enable'] and article_link:
                if self.is_stop_requested():
                    log_info("检测到停止请求，跳过下载任务")
                    return False

                try:
                    log_info(f"已提交全文下载任务: {article_link}")
                    self.add_to_download_queue(article_link, detail_name, official_account, url_hash)
                except Exception as e:
                    log_error(f"提交下载任务失败: {article_link} - {traceback.format_exc()}")

            # 保存数据
            self._save_article_data(csv_file_path, current_date, url_hash, detail_name, article_link)
            log_info(f'[{get_num}/{cache_num}] 已保存文章: {detail_name}')

            random_delay(*DEFAULT_DELAY_RANGE)
            return True

        except Exception as e:
            log_error(f"处理新文章失败: {traceback.format_exc()}")
            return False

    def _handle_page_change(self, detail_items_visibility: List, get_num: int,
                            official_window) -> bool:
        """处理翻页

        Args:
            detail_items_visibility: 可见元素列表
            get_num: 当前获取数量
            official_window: 公众号窗口

        Returns:
            bool: 是否成功翻页
        """
        page_change = False
        page_change_retry = MAX_PAGE_CHANGE_RETRY

        while page_change_retry > 0 and not page_change:
            page_change, _ = self._scroll_to_next(detail_items_visibility, get_num, official_window)
            page_change_retry -= 1

        return page_change

    def collect_official(self, official_infos: List[Dict], limit_count: int,
                         stop_exist_count: int, enable_time_limit: bool = False,
                         start_time: str = "", end_time: str = "") -> None:
        """采集公众号列表数据

        Args:
            official_infos: 公众号信息列表
            limit_count: 限制采集数量
            stop_exist_count: 停止存在计数
            enable_time_limit: 是否启用时间限制
            start_time: 开始时间
            end_time: 结束时间
        """
        self.reset_stop_flag()

        for official in official_infos:
            account = official['official_account']
            name = official['official_name']
            # 回写采集开始时间
            update_crawler_time(account, 'start', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            if self.is_stop_requested(enable_time_limit, start_time, end_time):
                log_info("检测到停止请求，退出采集")
                return

            log_info(f'开始采集公众号：{account}({name}) '
                     f'最大数量：{limit_count} 停止重复数：{stop_exist_count}')


            main_window = None
            official_window = None

            try:
                official_window, main_window = WechatTools.open_official_dialog_window(
                    official=name, weixin_path=self.weixin_path, is_maximize=False
                )

                if official_window:
                    main_window.close()
                    self.crawl_official_detail(official_window, account, name,
                                               limit_count, stop_exist_count)
                else:
                    log_warning(f'无该公众号：{account}({name})')

            except Exception as e:
                import traceback
                log_error(f'处理账号 {account} 时出错: {traceback.format_exc()}')
            finally:
                close_windows(official_window, main_window)

    def follow_official(self, accounts: List[str], enable_time_limit: bool = False,
                        start_time: str = "", end_time: str = "") -> List[Dict]:
        """关注公众号

        Args:
            accounts: 公众号账号列表
            enable_time_limit: 是否启用时间限制
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            List[Dict]: 关注结果列表
        """
        log_info("开始关注公众号")
        follow_results = []
        self.reset_stop_flag()

        search_window = WechatTools.open_official_account_follow(
            weixin_path=self.weixin_path, load_delay=3, is_maximize=False
        )
        desktop = Desktop(**Independent_window.Desktop)

        try:
            for account in accounts:
                if self.is_stop_requested(enable_time_limit, start_time, end_time):
                    log_info("检测到停止请求，退出关注")
                    break

                result = self._follow_single_account(account, search_window, desktop)
                if result:
                    follow_results.append(result)
            return follow_results
        finally:
            close_windows(search_window)

    def _follow_single_account(self, account: str, search_window, desktop) -> Optional[Dict]:
        """关注单个公众号账号

        Args:
            account: 公众号账号
            search_window: 搜索窗口
            desktop: 桌面对象

        Returns:
            Optional[Dict]: 关注结果，None表示失败
        """
        try:
            # 搜索公众号
            search = search_window.child_window(control_type='Edit', found_index=0)
            search.click_input()
            Systemsettings.copy_text_to_windowsclipboard(account)
            pyautogui.hotkey('ctrl', 'a')
            pyautogui.hotkey('ctrl', 'v')
            pyautogui.press('enter')

            random_delay(1, 2)

            # 点击公众号搜索结果
            search_result = search_window.child_window(title="公众号", control_type="Button")
            random_delay(1, 2)
            search_result.click_input()
            random_delay(1, 2)

            search_window.child_window(title=account, found_index=0, control_type="Text").click_input()
            random_delay(1, 2)

            # 关注公众号
            official_account_window = desktop.window(**Independent_window.NewOfficialAccountWindow)
            random_delay(1, 2)

            follow_button = official_account_window.child_window(title="关注", control_type="Button")
            official_name = official_account_window.child_window(
                control_type="Text", found_index=0
            ).element_info.name

            if follow_button.exists():
                follow_button.click_input()
                result = {
                    "official_account": account,
                    "official_name": official_name,
                }
                official_account_window.close()
                return result

            official_account_window.close()

        except ElementNotFoundError:
            log_error(f'查无此公众号: {account}')
        except Exception as e:
            log_error(f'关注公众号 {account} 失败: {traceback.format_exc()}')

        return None


if __name__ == '__main__':
    WechatTools.open_official_dialog_window(
        official='', weixin_path='D:\\Program Files\\Tencent\\Weixin\\WeChat.exe', is_maximize=False
    )