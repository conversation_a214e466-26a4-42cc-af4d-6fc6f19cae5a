
'''
weixin.weixin_ui_api
========
pywechat127下的与pywechat类似的微信4.0版本的自动化工具\n
目前仅实现了简单的发送消息打电话等功能\n
可前往 'https://github.com/Hello-Mr-Crab/pywechat' 查看详情\n
模块:\n
---------
WechatTools:该模块中封装了一系列关于4.0版本微信的工具,主要包括:检测微信运行状态;\n
打开微信主界面内绝大多数界面;打开指定公众号与微信小程序以及视频号\n
---------------
WechatAuto:pywechat的主要模块,其内部包含了:\n
    Messages:5种类型的发送消息功能包括:单人单条,单人多条,多人单条,多人多条,转发消息:多人同一条消息\n
    Files:5种类型的发送文件功能包括:单人单个,单人多个,多人单个,多人多个,转发文件:多人同一个文件\n
    Call:给某个好友打视频或语音电话,在群聊内发起语音电话\n
---------------------
Winsettings:一些修改windows系统设置的方法\n
----------------------
Uielements:微信主界面内UI的封装\n
-----------------
Clocks:用于实现pyweixin内所有方法定时操作的模块\n
-----------------
Warnings:一些可能触发的警告\n
-----------------------
支持版本
---------------
OS-Version:window10,windows11\n
----------------------------
Python-version:3.x,\tWechatVersion:4.0.5.18\n
----------------------------------
Have fun in WechatAutomation (＾＿－)
====
'''
from weixin.weixin_ui_api.WechatAuto import *
from weixin.weixin_ui_api.WechatTools import *
from weixin.weixin_ui_api.WinSettings import *
from weixin.weixin_ui_api.Clock import *
#Author:Hello-Mr-Crab
#version:1.9.6
#4.0微信主界面:
#=============================================================================  
# ToolBar |搜索|       |+|                 ··· ||
#      ======================                     ||
#|头像|   ||         ||                     ||
#|聊天|   ||         ||                     ||
#|通讯录|  || 会话列表    ||                     ||
#|收藏|   ||         ||      与好友的聊天界面       ||
#|聊天文件| ||         ||                     ||
#|朋友圈|  ||         ||                     ||
#|视频号|  ||         ||                     ||
#|看一看|  ||         ||                     ||
#|搜一搜|  ||         ||                     ||
#      ||         ||                     ||
#      ||         ||                     ||
#      ||         ||                     ||
#      ||         ||------------------------------------------||
#|小程序面板|||         ||表情 聊天文件 截图 聊天记录      ||
#|手机|   ||         ||                     ||
#|设置及其他|||         ||                     ||
#==============================================================================


