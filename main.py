"""
微信公众号自动化管理工具
基于PySide6的Qt应用程序
"""
import logging
import sys

from PySide6.QtCore import Qt
from PySide6.QtWidgets import QApplication, QMessageBox

# 初始化COM组件，避免pywinauto冲突
from core.com_init import safe_initialize_com, get_com_status
from ui.main_window import MainWindow

# 在应用启动时初始化日志
from core.common_util import setup_logger, log_info

# 初始化日志系统
logger = setup_logger()
log_info("应用程序启动")

com_success = safe_initialize_com()

# 输出COM状态信息（用于调试）
if __name__ == "__main__":
    status = get_com_status()
    print(f"COM状态: {status}")
    if not com_success:
        print("警告: COM初始化失败，部分功能可能受影响")


def main():
    """主函数"""
    # 检查是否是子进程工作模式
    if len(sys.argv) >= 3 and sys.argv[1] == '--subprocess-worker':
        # 子进程工作模式
        config_file = sys.argv[2]
        print(f"[DEBUG] 子进程工作模式，配置文件: {config_file}")

        # 导入并执行子进程工作逻辑
        try:
            from core.subprocess_worker import main as worker_main
            # 修改sys.argv以匹配subprocess_worker的期望
            sys.argv = ['subprocess_worker.py', config_file]
            worker_main()
        except Exception as e:
            print(f"[ERROR] 子进程执行失败: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)
        return

    # 正常的GUI模式
    # 在创建QApplication之前设置高DPI支持（PySide6推荐方式）
    QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)

    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("微信公众号自动化管理工具")
    app.setApplicationVersion("1.0.0")
    try:
        # 检查COM状态
        from core.com_init import get_com_status
        status = get_com_status()
        print(f"COM状态检查: {status}")

        # 创建主窗口
        main_window = MainWindow()
        main_window.show()

        # 运行应用程序
        result = app.exec()

        # 清理COM组件
        try:
            from core.com_init import cleanup_com
            cleanup_com()
        except Exception as e:
            print(f"清理COM组件时出错: {e}")

        sys.exit(result)

    except Exception as e:
        logging.exception("应用程序启动失败")
        QMessageBox.critical(None, "错误", f"应用程序启动失败: {str(e)}")

        # 清理COM组件
        try:
            from core.com_init import cleanup_com
            cleanup_com()
        except Exception as e:
            logging.exception("应用程序启动失败")

        sys.exit(1)


if __name__ == '__main__':
    # 支持多进程（Windows下需要）
    main()
