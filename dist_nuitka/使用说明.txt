微信公众号自动化管理工具 - 使用说明

🚀 快速开始:
1. 双击"微信公众号自动化管理工具.exe"启动程序
2. 首次使用请在"设置"中配置微信程序路径

📁 目录说明:
- resource/ : 配置文件目录
  - config.json : 程序配置
  - accounts.csv : 账号数据
  - official_infos.json : 公众号信息
- data/ : 数据存储目录
  - *.csv : 各账号采集数据

📋 主要功能:
- 账号管理: 添加、编辑、删除公众号账号
- 批量关注: 自动关注指定的公众号列表  
- 数据采集: 自动采集公众号信息和文章数据
- 数据查看: 查看、搜索、筛选、导出数据
- 全文下载: 下载文章全文为MD/HTML格式

💻 系统要求:
- Windows 10/11
- 微信PC版 (最新版本)

⚠️ 重要提醒:
- 使用时请遵守微信使用条款，避免频繁操作
- 首次启动可能需要几秒钟，请耐心等待

🔧 技术信息:
- 打包工具: Nuitka
- Python版本: 3.12
- 构建时间: D:\Project\PycharmProjects\lawyee\Wechat-Auto

如有问题，请查看README.md文档或联系技术支持。
