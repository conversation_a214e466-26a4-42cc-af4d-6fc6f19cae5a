# 微信公众号自动化管理工具

[![Python](https://img.shields.io/badge/Python-3.12+-blue.svg)](https://www.python.org/)
[![PySide6](https://img.shields.io/badge/PySide6-6.9.0-green.svg)](https://pypi.org/project/PySide6/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/Platform-Windows-lightgrey.svg)](https://www.microsoft.com/windows)

一个基于 Python 和 PySide6 开发的微信公众号自动化管理工具，支持批量关注公众号、自动采集公众号内容、数据管理和全文下载等功能。

## ✨ 主要功能

### 📱 公众号管理
- **账号管理**: 添加、编辑、删除公众号账号信息
- **批量导入**: 支持从CSV文件批量导入公众号账号
- **数据验证**: 自动验证账号格式和有效性
- **账号分组**: 支持按类别管理公众号

### 🤖 自动化操作
- **批量关注**: 自动批量关注指定的公众号列表
- **智能采集**: 自动采集公众号文章信息和内容
- **进度监控**: 实时显示任务进度和执行状态
- **错误处理**: 智能错误处理和重试机制
- **时间控制**: 支持设置运行时间段

### 📊 数据管理
- **数据查看**: 查看、搜索、筛选采集到的数据
- **数据导出**: 支持导出为CSV、Excel等格式
- **数据去重**: 自动去除重复的文章链接
- **数据备份**: 定期备份数据文件
- **统计分析**: 提供数据统计和分析功能

### 📄 内容下载
- **全文下载**: 下载文章全文为Markdown或HTML格式
- **批量处理**: 支持批量下载多篇文章
- **格式转换**: 自动转换HTML为Markdown格式
- **文件管理**: 按公众号分类存储下载的文件
- **图片下载**: 自动下载文章中的图片

### ⚙️ 系统功能
- **任务调度**: 支持定时任务和计划任务
- **日志记录**: 详细的操作日志和错误记录
- **配置管理**: 灵活的配置选项和参数设置
- **界面友好**: 现代化的图形用户界面
- **多线程**: 支持多线程并发处理

## 🚀 快速开始

### 系统要求

- **操作系统**: Windows 10/11 (64位)
- **Python版本**: 3.12+
- **微信版本**: 微信PC版 (最新版本)
- **内存**: 建议4GB以上
- **存储**: 建议2GB以上可用空间

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/Wechat-Auto.git
   cd Wechat-Auto
   ```

2. **安装依赖**
   ```bash
   # 方式一：使用安装脚本（推荐）
   python install_deps.py
   
   # 方式二：直接安装
   pip install -r requirements.txt
   ```

3. **运行程序**
   ```bash
   python main.py
   ```

### 首次使用

1. **配置微信路径**: 程序会自动检测微信安装路径，如检测失败请手动设置
2. **添加公众号**: 在"账号管理"页面添加要关注或采集的公众号
3. **配置参数**: 在"设置"中配置采集参数和下载选项
4. **开始使用**: 选择相应功能开始自动化操作

## 📖 使用指南

### 账号管理

1. **添加单个账号**
   - 点击"添加账号"按钮
   - 填写公众号名称和账号
   - 选择分类标签
   - 点击"保存"

2. **批量导入账号**
   - 准备CSV文件，格式：`公众号名称,公众号账号,分类`
   - 点击"导入CSV"选择文件
   - 预览导入数据
   - 确认导入

3. **账号管理**
   - 编辑：双击账号行进行编辑
   - 删除：选中账号后点击删除按钮
   - 搜索：使用搜索框快速查找账号

### 批量关注

1. **选择账号**: 在关注管理页面选择要关注的公众号
2. **设置参数**: 配置关注间隔、重试次数等参数
3. **开始关注**: 点击"开始关注"按钮
4. **监控进度**: 查看实时进度和日志信息
5. **查看结果**: 关注完成后查看成功和失败的统计

### 数据采集

1. **配置参数**:
   - 设置最大采集数量
   - 设置停止条件
   - 选择要采集的公众号
   - 配置时间范围

2. **开始采集**: 点击"开始采集"按钮
3. **实时监控**: 查看采集进度和状态
4. **数据查看**: 在数据查看器中查看采集结果

### 全文下载

1. **启用下载**: 在设置中启用全文下载功能
2. **选择格式**: 选择下载格式（Markdown/HTML）
3. **设置延迟**: 配置下载间隔避免频繁请求
4. **批量下载**: 在数据查看器中选择文章进行批量下载

### 数据查看与导出

1. **查看数据**: 在"数据查看"页面浏览采集的数据
2. **搜索筛选**: 使用搜索功能快速找到需要的内容
3. **排序**: 按时间、阅读量等字段排序
4. **导出数据**: 选择数据后点击"导出"按钮

## 🏗️ 项目结构

```
Wechat-Auto/
├── main.py                    # 主程序入口
├── requirements.txt           # 生产环境依赖
├── requirements-dev.txt       # 开发环境依赖
├── requirements-minimal.txt   # 最小化依赖
├── install_deps.py           # 依赖安装脚本
├── build_nuitka.py           # Nuitka打包脚本
├── config/                   # 配置模块
│   └── settings.py          # 设置管理
├── core/                     # 核心功能模块
│   ├── account_service.py   # 账号服务
│   ├── official_service.py  # 公众号服务
│   ├── task_manager.py      # 任务管理
│   ├── data_manager.py      # 数据管理
│   ├── official_down.py     # 文章下载
│   ├── wechat_detector.py   # 微信检测
│   ├── scheduler.py         # 任务调度
│   ├── com_init.py          # COM组件初始化
│   ├── common_util.py       # 通用工具和日志
│   └── subprocess_worker.py # 子进程工作器
├── ui/                       # 用户界面模块
│   ├── main_window.py       # 主窗口
│   ├── account_manager.py   # 账号管理界面
│   ├── follow_manager.py    # 关注管理界面
│   ├── collect_manager.py   # 采集管理界面
│   ├── data_viewer.py       # 数据查看界面
│   ├── settings_dialog.py   # 设置对话框
│   ├── message_box.py       # 消息框组件
│   └── components/          # UI组件
├── weixin/                   # 微信自动化模块
│   ├── auto_process/        # 自动化处理
│   │   └── officical_process.py  # 公众号处理
│   └── weixin_ui_api/       # 微信UI API
│       ├── WechatAuto.py    # 微信自动化
│       ├── WechatTools.py   # 微信工具
│       └── __init__.py      # 模块初始化
├── resource/                 # 资源文件
│   ├── config.json          # 配置文件
│   ├── accounts.csv         # 账号数据
│   └── official_infos.json  # 公众号信息
├── data/                     # 数据存储目录
├── downloads/                # 下载文件目录
├── logs/                     # 日志文件目录
└── dist_nuitka/             # 打包输出目录
```

## ⚙️ 技术栈

### 核心框架
- **GUI框架**: PySide6 (Qt for Python)
- **自动化**: pywinauto, pyautogui, uiautomation
- **Windows API**: pywin32, comtypes

### 数据处理
- **数据分析**: pandas, numpy
- **文件处理**: openpyxl, python-docx
- **配置管理**: PyYAML, python-dotenv

### 网络和爬虫
- **HTTP请求**: requests, httpx, aiohttp
- **网页解析**: BeautifulSoup4, lxml
- **浏览器自动化**: selenium, playwright
- **文本转换**: html2text

### 图像和OCR
- **图像处理**: opencv-python, Pillow
- **OCR识别**: ddddocr, paddleocr, pytesseract

### 开发工具
- **打包工具**: Nuitka, PyInstaller
- **日志系统**: loguru, colorlog
- **任务调度**: QTimer, threading
- **进度显示**: tqdm

## 🔧 配置说明

### 主要配置项

```json
{
    "wechat": {
        "wechat_path": "",        // 微信程序路径
        "load_delay": 3,          // 加载延迟(秒)
        "is_maximize": false      // 是否最大化窗口
    },
    "task": {
        "limit_count": 200,       // 最大采集数量
        "stop_exist_count": 30,   // 停止存在计数
        "enable_time_limit": false, // 启用时间限制
        "start_time": "09:00",    // 开始时间
        "end_time": "18:00"       // 结束时间
    },
    "download": {
        "enable": true,           // 启用全文下载
        "format": "MD+HTML",      // 下载格式
        "delay": 2,               // 下载延迟(秒)
        "max_retries": 3          // 最大重试次数
    },
    "ui": {
        "theme": "light",         // 界面主题
        "auto_size": true,        // 自动调整窗口大小
        "language": "zh_CN"       // 界面语言
    },
    "logging": {
        "level": "INFO",          // 日志级别
        "max_files": 30,          // 最大日志文件数
        "file_size": "10MB"       // 单个日志文件大小
    }
}
```

### 环境变量

- `WECHAT_PATH`: 微信程序路径
- `DEBUG`: 调试模式开关
- `LOG_LEVEL`: 日志级别

## 📝 开发指南

### 开发环境搭建

1. **安装开发依赖**
   ```bash
   python install_deps.py --dev
   ```

2. **代码格式化**
   ```bash
   black .
   isort .
   ```

3. **代码检查**
   ```bash
   flake8 .
   mypy .
   ```

4. **运行测试**
   ```bash
   pytest tests/ -v --cov=.
   ```

### 项目架构

- **MVC模式**: 界面(UI) - 业务逻辑(Core) - 数据(Data)
- **模块化设计**: 功能模块独立，便于维护和扩展
- **异步处理**: 使用多线程处理耗时操作
- **事件驱动**: 基于Qt信号槽机制

### 添加新功能

1. **核心逻辑**: 在 `core/` 目录添加业务逻辑
2. **界面组件**: 在 `ui/` 目录添加界面组件
3. **配置项**: 在 `config/settings.py` 添加配置
4. **测试用例**: 在 `tests/` 目录添加测试

### 打包发布

```bash
# 使用Nuitka打包（推荐）
python build_nuitka.py

# 使用PyInstaller打包
pyinstaller --onefile --windowed main.py
```

## 📊 性能优化

### 内存优化
- 使用生成器处理大量数据
- 及时释放不需要的对象
- 限制并发线程数量

### 速度优化
- 使用多线程并发处理
- 缓存常用数据
- 优化数据库查询

### 稳定性优化
- 异常处理和重试机制
- 资源清理和内存管理
- 日志记录和错误追踪

## ⚠️ 注意事项

### 使用限制
- **合规使用**: 请遵守微信使用条款，避免频繁操作
- **适度使用**: 建议在操作间隔中加入适当延迟
- **单实例运行**: 不要同时运行多个程序实例
- **网络环境**: 确保网络连接稳定

### 安全提醒
- **个人使用**: 请勿用于商业用途或大规模采集
- **隐私保护**: 注意保护个人隐私和数据安全
- **数据备份**: 建议定期备份重要数据
- **版本更新**: 及时更新到最新版本

### 兼容性
- **系统要求**: 仅支持Windows 10/11系统
- **微信版本**: 需要安装微信PC版最新版本
- **权限要求**: 可能需要管理员权限
- **防火墙**: 确保防火墙允许程序网络访问

### 故障排除

1. **程序无法启动**
   - 检查Python版本是否为3.12+
   - 确认所有依赖已正确安装
   - 查看日志文件获取错误信息

2. **微信检测失败**
   - 确认微信已安装并可正常运行
   - 检查微信路径配置是否正确
   - 尝试以管理员权限运行

3. **采集数据异常**
   - 检查网络连接是否正常
   - 确认公众号账号是否有效
   - 查看操作日志了解具体错误

4. **界面显示异常**
   - 检查显示器分辨率和缩放设置
   - 尝试重置界面配置
   - 更新显卡驱动程序

## 🐛 问题反馈

如果您在使用过程中遇到问题，请：

1. **查看日志**: 检查 `logs/` 目录下的日志文件
2. **检查配置**: 确认 `resource/config.json` 配置正确
3. **版本兼容**: 确认微信版本和系统版本兼容性
4. **提交Issue**: 在GitHub上提交Issue并附上详细信息

### Issue模板

```
**问题描述**
简要描述遇到的问题

**复现步骤**
1. 执行了什么操作
2. 期望的结果
3. 实际的结果

**环境信息**
- 操作系统: Windows 10/11
- Python版本: 3.12.x
- 微信版本: x.x.x
- 程序版本: x.x.x

**日志信息**
粘贴相关的日志信息

**截图**
如果适用，请添加截图说明问题
```

## 🤝 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. **Fork项目**: 点击右上角Fork按钮
2. **创建分支**: `git checkout -b feature/your-feature`
3. **提交代码**: `git commit -am 'Add some feature'`
4. **推送分支**: `git push origin feature/your-feature`
5. **创建PR**: 在GitHub上创建Pull Request

### 代码规范
- 使用Black进行代码格式化
- 遵循PEP 8编码规范
- 添加必要的注释和文档
- 编写单元测试

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢以下开源项目的支持：
- [PySide6](https://pypi.org/project/PySide6/) - Qt for Python
- [pywinauto](https://github.com/pywinauto/pywinauto) - Windows应用自动化
- [BeautifulSoup](https://www.crummy.com/software/BeautifulSoup/) - HTML解析
- [pandas](https://pandas.pydata.org/) - 数据分析
- [requests](https://requests.readthedocs.io/) - HTTP库

## 📞 联系方式

- **项目主页**: [GitHub Repository](https://github.com/your-username/Wechat-Auto)
- **问题反馈**: [Issues](https://github.com/your-username/Wechat-Auto/issues)
- **讨论交流**: [Discussions](https://github.com/your-username/Wechat-Auto/discussions)

## 📈 更新日志

### v1.0.0 (2024-01-XX)
- 🎉 首次发布
- ✨ 支持公众号批量关注
- ✨ 支持内容自动采集
- ✨ 支持全文下载
- ✨ 现代化GUI界面

---

**免责声明**: 本工具仅供学习和研究使用，请遵守相关法律法规和平台使用条款。使用本工具产生的任何后果由使用者自行承担。

**⭐ 如果这个项目对您有帮助，请给个Star支持一下！**