"""
数据管理器
负责处理data目录中的账号数据文件
"""
import json
import os
import csv
import shutil
from typing import List, Dict, Set, Tuple, Optional, Any
from datetime import datetime
import logging


class DataManager:
    """数据管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.data_dir = "data"
        self.ensure_directories()
    
    def ensure_directories(self):
        """确保必要的目录存在"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)

    def get_all_data_files(self) -> List[Tuple[str, str]]:
        """获取所有数据文件列表

        Returns:
            List[Tuple[str, str]]: (文件路径, 显示名称) 的列表
        """
        files = []

        # 读取现有的official_infos.json
        official_infos_file = os.path.join(os.getcwd(), 'resource', 'official_infos.json')

        # 确保resource目录存在
        os.makedirs(os.path.dirname(official_infos_file), exist_ok=True)

        existing_infos = []
        if os.path.exists(official_infos_file):
            try:
                with open(official_infos_file, 'r', encoding='utf-8') as f:
                    existing_infos = json.load(f)
            except Exception as e:
                self.logger.error(f"读取official_infos.json失败: {e}")
                existing_infos = []

        # 添加账号文件
        if os.path.exists(self.data_dir):
            for filename in sorted(os.listdir(self.data_dir)):
                if filename.endswith('.csv'):
                    file_path = os.path.join(self.data_dir, filename)
                    account_id = filename[:-4]  # 去掉.csv后缀

                    # 查找匹配的公众号信息
                    matched_info = [info for info in existing_infos
                                    if info.get('official_account') == account_id]
                    if matched_info:
                        display_name = f"{matched_info[0].get('official_name', account_id)}({filename})"
                    else:
                        display_name = f"{account_id} ({filename})"

                    files.append((file_path, display_name))
        return files

    def read_csv_data(self, file_path: str) -> list[Any]:
        data = []
        
        if not os.path.exists(file_path):
            return data
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                for row in reader:
                    # 确保行数据与表头数量一致
                    data.append(row)
        
        except Exception as e:
            self.logger.error(f"读取CSV文件失败 {file_path}: {e}")
            raise
        
        return data
    
    def write_csv_data(self, file_path: str, data: List[List[str]]):
        """写入CSV文件数据
        
        Args:
            file_path: CSV文件路径
            data: 数据行
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerows(data)
            
            self.logger.info(f"成功写入CSV文件 {file_path}, {len(data)} 条记录")
        
        except Exception as e:
            self.logger.error(f"写入CSV文件失败 {file_path}: {e}")
            raise
    
    def deduplicate_by_link(self, data: List[List[str]], link_column_index: int = 2) -> List[List[str]]:
        """基于链接去重
        
        Args:
            data: 原始数据
            link_column_index: 链接列的索引
            
        Returns:
            List[List[str]]: 去重后的数据
        """
        seen_links = set()
        deduplicated_data = []
        
        for row in data:
            if len(row) > link_column_index:
                link = row[link_column_index].strip()
                if link and link not in seen_links:
                    seen_links.add(link)
                    deduplicated_data.append(row)
            else:
                # 如果没有链接列，保留数据
                deduplicated_data.append(row)
        
        return deduplicated_data
    
    def backup_data_files(self, backup_dir: str = None) -> str:
        """备份数据文件
        
        Args:
            backup_dir: 备份目录，如果为None则自动生成
            
        Returns:
            str: 备份目录路径
        """
        if backup_dir is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_dir = f"backup_{timestamp}"
        
        os.makedirs(backup_dir, exist_ok=True)
        
        # 备份data目录
        if os.path.exists(self.data_dir):
            backup_data_dir = os.path.join(backup_dir, "data")
            shutil.copytree(self.data_dir, backup_data_dir, dirs_exist_ok=True)
        
        self.logger.info(f"数据文件已备份到: {backup_dir}")
        return backup_dir
    
    def get_file_stats(self, file_path: str) -> Dict[str, any]:
        """获取文件统计信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict: 统计信息
        """
        stats = {
            'exists': False,
            'size': 0,
            'record_count': 0,
            'last_modified': None,
            'account_count': 0
        }
        
        if not os.path.exists(file_path):
            return stats
        
        stats['exists'] = True
        stats['size'] = os.path.getsize(file_path)
        stats['last_modified'] = datetime.fromtimestamp(os.path.getmtime(file_path))
        
        try:
            data = self.read_csv_data(file_path)
            stats['record_count'] = len(data)
            
            # 统计账号数量
            accounts = set()
            for row in data:
                if len(row) > 0:
                    accounts.add(row[0].strip())
            stats['account_count'] = len(accounts)
        
        except Exception as e:
            self.logger.error(f"获取文件统计失败 {file_path}: {e}")
        
        return stats
    
    def clean_invalid_data(self, file_path: str) -> int:
        """清理无效数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            int: 清理的记录数
        """
        if not os.path.exists(file_path):
            return 0
        
        data = self.read_csv_data(file_path)
        original_count = len(data)
        
        # 清理规则
        cleaned_data = []
        for row in data:
            # 跳过空行
            if not any(cell.strip() for cell in row):
                continue
            
            # 跳过无效链接的行
            if len(row) > 2:
                link = row[2].strip()
                if link and not link.startswith('http'):
                    continue
            
            cleaned_data.append(row)
        
        cleaned_count = original_count - len(cleaned_data)
        
        if cleaned_count > 0:
            self.write_csv_data(file_path, cleaned_data)
            self.logger.info(f"清理了 {cleaned_count} 条无效记录")
        
        return cleaned_count


# 全局数据管理器实例
data_manager = DataManager()
