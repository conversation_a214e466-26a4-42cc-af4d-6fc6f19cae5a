import json
import logging
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path


# 配置日志记录器
def setup_logger():
    """设置日志记录器"""
    # 确保logs目录存在
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    # 创建日志文件名（按日期）
    log_filename = log_dir / f"app_{datetime.now().strftime('%Y%m%d')}.log"

    # 配置日志格式
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'

    # 配置文件处理器
    file_handler = logging.FileHandler(log_filename, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(logging.Formatter(log_format, date_format))

    # 获取或创建logger
    logger = logging.getLogger('app_logger')
    logger.setLevel(logging.INFO)

    # 避免重复添加处理器
    if not logger.handlers:
        logger.addHandler(file_handler)

    return logger


# 初始化全局logger
_app_logger = setup_logger()


def send_message(msg_type='log', message='', progress=None, level='INFO'):
    """发送消息到主进程并记录到日志文件

    Args:
        msg_type: 消息类型 ('log', 'progress', 'error')
        message: 消息内容
        progress: 进度值 (0-100)
        level: 日志级别 ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
    """
    msg = {
        'type': msg_type,
        'message': message
    }
    if progress is not None:
        msg['progress'] = progress

    # 记录到日志文件
    if msg_type == 'log' and message:
        try:
            log_level = getattr(logging, level.upper(), logging.INFO)
            _app_logger.log(log_level, message)
        except Exception:
            # 日志记录失败不应影响主程序
            pass

    try:
        # 统一使用UTF-8编码发送消息
        output = json.dumps(msg, ensure_ascii=False, separators=(',', ':')) + '\n'
        sys.stdout.buffer.write(output.encode('utf-8'))
        sys.stdout.flush()
    except (UnicodeEncodeError, TypeError):
        # 处理编码错误，使用ASCII编码作为备用方案
        try:
            print(json.dumps(msg, ensure_ascii=True))
            sys.stdout.flush()
        except Exception:
            # 如果仍然失败，记录错误但不中断程序
            try:
                _app_logger.error(f"发送消息失败: {msg_type} - {message}")
            except Exception:
                pass


def log_info(message: str):
    """记录INFO级别日志"""
    send_message('log', message, level='INFO')


def log_warning(message: str):
    """记录WARNING级别日志"""
    send_message('log', message, level='WARNING')


def log_error(message: str):
    """记录ERROR级别日志"""
    send_message('log', message, level='ERROR')


def log_debug(message: str):
    """记录DEBUG级别日志"""
    send_message('log', message, level='DEBUG')


def log_critical(message: str):
    """记录CRITICAL级别日志"""
    send_message('log', message, level='CRITICAL')


def cleanup_old_logs(days_to_keep=30):
    """清理旧的日志文件

    Args:
        days_to_keep: 保留的天数，默认30天
    """
    try:
        log_dir = Path("logs")
        if not log_dir.exists():
            return

        cutoff_date = datetime.now() - timedelta(days=days_to_keep)

        for log_file in log_dir.glob("app_*.log"):
            try:
                # 从文件名提取日期
                date_str = log_file.stem.split('_')[1]
                file_date = datetime.strptime(date_str, '%Y%m%d')

                if file_date < cutoff_date:
                    log_file.unlink()
                    _app_logger.info(f"已删除过期日志文件: {log_file.name}")
            except (ValueError, IndexError):
                # 文件名格式不正确，跳过
                continue

    except Exception as e:
        _app_logger.error(f"清理日志文件失败: {str(e)}")

def update_crawler_time(official_account: str, time_type: str, time_value: str = None):
    """
    更新指定official_account的爬虫时间信息

    Args:
        official_account (str): 公众号账号
        time_type (str): 时间类型，'start'表示开始时间，'end'表示结束时间
        time_value (str, optional): 时间值，默认为当前时间
    """
    # 读取现有的official_infos.json
    official_infos_file = os.path.join(os.getcwd(), 'resource', 'official_infos.json')
    # 读取json文件
    with open(official_infos_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    # 查找对应的official_account
    find_account = False
    for item in data:
        if item.get('official_account') == official_account:
            find_account = True
            log_info(f"正在更新公众号 {official_account} 的爬虫时间信息")
            # 根据类型添加对应的时间字段
            if time_type == 'start':
                item['crawler_start_time'] = time_value or datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            elif time_type == 'end':
                item['crawler_end_time'] = time_value or datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            break
    if not find_account:
        log_info(f"未找到公众号 {official_account}")

    # 写回文件
    with open(official_infos_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
# 在模块加载时清理旧日志
cleanup_old_logs()